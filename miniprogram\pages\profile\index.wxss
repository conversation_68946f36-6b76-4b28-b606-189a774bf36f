.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-top: 20rpx;
}

.profile-list {
  background: #fff;
  padding: 0 30rpx;
}

.profile-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.profile-item:last-child {
  border-bottom: none;
}

.profile-item .label {
  font-size: 28rpx;
  color: #333;
}

.profile-item .value {
  display: flex;
  align-items: center;
}

.profile-item .value text {
  font-size: 28rpx;
  color: #666;
}

.name-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.committee-badge {
  background: linear-gradient(135deg, #ff9500, #ff7300);
  color: #fff;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.3);
  white-space: nowrap;
}

/* 头像项样式 */
.avatar-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.avatar-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-text {
  font-size: 28rpx;
  color: #666;
}

/* 昵称项样式 */
.nickname-item .value {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nickname-input {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #666;
  text-align: right;
  border: none;
  background: transparent;
  padding: 0;
}

/* 手机号和认证操作样式 */
.phone-action,
.auth-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
}

.btn-text {
  color: #07c160 !important;
  margin-left: 20rpx;
}

.auth-text {
  color: #07c160 !important;
}

/* 手机号绑定弹窗 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  text-align: center;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.popup-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.bind-phone-btn {
  width: 100% !important;
  height: 88rpx;
  line-height: 88rpx;
  background: #07c160 !important;
  color: #fff !important;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-bottom: 30rpx;
  font-weight: 500;
  border: none;
}

.bind-phone-btn::after {
  border: none;
}

.popup-close {
  font-size: 28rpx;
  color: #999;
  padding: 20rpx;
}

/* 授权按钮样式 */
.auth-btn {
  width: 100% !important;
  height: 88rpx;
  line-height: 88rpx;
  background: #07c160 !important;
  color: #fff !important;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-bottom: 30rpx;
  font-weight: 500;
  border: none;
}

.auth-btn::after {
  border: none;
}

/* 昵称编辑输入框样式 */
.nickname-edit-input {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  margin-bottom: 40rpx;
  box-sizing: border-box;
  background: #fafafa;
}

/* 弹窗按钮组 */
.popup-buttons {
  display: flex;
  gap: 20rpx;
}

.popup-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #07c160;
  color: #fff;
}

