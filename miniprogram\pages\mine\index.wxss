/* pages/mine/index.wxss */
page {
  background: #f7f8fa;
  height: 100vh;
}

.container {
  min-height: 100vh;
  background: #f7f8fa;
}

/* 顶部整体区域 */
.header-wrapper {
  position: relative;
  background: linear-gradient(135deg, #1890ff, #69c0ff);
  margin-bottom: 24rpx;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.1;
}

/* 自定义导航栏 */
.custom-navbar {
  position: relative;
  z-index: 100;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 26rpx;
  position: relative;
  z-index: 2;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* 用户头部 */
.user-header {
  position: relative;
  padding: 30rpx 30rpx 0 30rpx;
  z-index: 2;
  margin-bottom: -40rpx; /* 负边距实现重叠效果 */
}

.user-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10; /* 确保在上层 */
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
  margin-right: 32rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
}

.user-info {
  flex: 1;
}

.nickname-container {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.nickname {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-right: 16rpx;
}

.committee-badge {
  background: linear-gradient(135deg, #ff9500, #ff7300);
  color: #fff;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.3);
  white-space: nowrap;
}

.phone {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.house-info {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.area-info {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 0;
  display: block;
}

.status {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.25);
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  display: inline-block;
  backdrop-filter: blur(8px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  font-weight: 500;
}

.status.auth {
  background: rgba(255, 255, 255, 0.35);
  color: #fff;
}

.login-btn {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.3);
  padding: 24rpx 48rpx;
  border-radius: 50rpx;
  font-weight: 600;
  backdrop-filter: blur(8px);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.login-btn:active {
  background: rgba(255, 255, 255, 0.4);
  transform: scale(0.98);
}

/* 分区标题 */
.section-title {
  font-size: 28rpx;
  color: #666;
  padding: 70rpx 30rpx 30rpx 30rpx; /* 增加上边距以配合重叠效果 */
  font-weight: 500;
}

/* 菜单列表 */
.menu-list {
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.02);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #fafafa;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-item-left van-icon {
  margin-right: 20rpx;
  width: 48rpx;
  text-align: center;
}

.menu-item-left text {
  font-size: 28rpx;
  color: #333;
}

.menu-item-right {
  display: flex;
  align-items: center;
}

.menu-item-right .desc {
  font-size: 26rpx;
  color: #999;
  margin-right: 12rpx;
}

.menu-item-right .icon-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 联系客服按钮 */
.contact-btn {
  font-weight: 400;
  width: 100% !important;
  background: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  border-radius: 0;
  border-bottom: none; /* 确保最后一个菜单项没有下边框 */
}

.contact-btn::after {
  display: none;
}



/* 退出登录按钮 */
.logout-btn {
  margin: 40rpx 30rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #fff;
  color: #ff4d4f;
  font-size: 28rpx;
  border-radius: 44rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.02);
}

.logout-btn:active {
  background: #fafafa;
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0;
  margin-top: auto;
}

/* 所有iconfont图标已移除，改用Vant图标 */