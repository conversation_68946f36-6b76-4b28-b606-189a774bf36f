import {getStateManager} from '../../utils/stateManager.js'
import {getLoadingManager, handleError} from '../../utils/errorHandler.js'
import SecureLogger from '../../utils/secureLogger.js'
import {getLoginService} from '../../services/loginService.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()
const loginService = getLoginService()

// 缓存键名常量
const STORAGE_KEYS = {
  POLICY_AGREEMENT: 'policyAgreement'
}

Page({
  data: {
    isLogin: false,
    hasBindPhone: false,
    showPhoneModal: false,
    showHouseAuthModal: false,
    isLogging: false,
    // 隐私政策同意状态
    agreedToPolicy: false,
    // 用户信息授权相关
    showUserInfoModal: false,
    showAvatarPopup: false,
    showNicknamePopup: false,
    tempNickname: '',
    currentUserInfo: null,
    // 待处理的登录数据（新用户授权后使用）
    pendingLoginData: null
  },

  onLoad() {
    this.loadPolicyAgreementFromCache()
    this.checkAutoLogin()
  },

  onShow() {
    this.refreshLoginState()
  },

  // 检查自动登录（使用LoginService）
  async checkAutoLogin() {
    try {
      const success = await loginService.checkAutoLogin()
      if (success) {
        this.refreshLoginState()
        loginService.redirectToHome()
      } else {
        this.refreshLoginState()
      }
    } catch (error) {
      SecureLogger.error('Login', '自动登录检查失败', error)
      this.refreshLoginState()
    } finally {
      loadingManager.hide()
    }
  },

  // 注意：isTokenExpiredLocally方法已移至LoginService

  // 刷新登录状态
  refreshLoginState() {
    const state = stateManager.getState()
    this.setData({
      isLogin: state.isLogin,
      hasBindPhone: state.hasBindPhone
    })
  },

  /**
   * 智能登录主流程入口
   * 功能描述：
   * - 获取微信登录凭证（wx.login）
   * - 调用后端智能登录接口验证用户身份
   * - 老用户直接登录成功，新用户进入注册流程
   * - 处理登录过程中的各种异常情况
   */
  async step1_handleSmartLogin() {
    if (this.data.isLogging) {
      SecureLogger.log('Login', '登录正在进行中，忽略重复点击')
      return
    }

    // 检查是否已同意隐私政策
    if (!this.data.agreedToPolicy) {
      wx.showToast({
        title: '请先同意用户协议和隐私政策',
        icon: 'none',
        duration: 2000
      })
      return
    }

    try {
      this.setData({ isLogging: true })

      // 获取微信登录凭证
      loadingManager.show('获取登录凭证...')
      const loginResult = await wx.login()

      if (!loginResult.code) {
        throw new Error('获取微信登录凭证失败')
      }

      // 使用LoginService执行登录
      SecureLogger.log('Login', '开始智能登录流程')
      const loginData = await loginService.performWxLogin({
        code: loginResult.code
      })

      // 检查是否需要手机号授权
      if (loginData && loginData.needPhoneAuth) {
        SecureLogger.log('Login', '检测到新用户，需要手机号授权')

        // 保存登录数据用于后续流程（不保存已使用的code，避免重复使用）
        this.setData({
          isLogging: false,
          pendingLoginData: {
            checkResult: loginData.checkResult
          }
        })
        loadingManager.hide()

        // 开始新用户授权流程
        this.step1_1_startNewUserRegistration()
        return
      }

      // 登录成功，处理结果
      SecureLogger.log('Login', '智能登录成功')
      const processedData = loginService.handleLoginSuccess(loginData)
      this.step2_analyzeLoginResult(processedData)

    } catch (error) {
      SecureLogger.error('Login', '智能登录失败', error)
      handleError(error, '登录')

      // 清除登录状态和待处理数据
      loginService.clearLoginState()
      this.setData({ pendingLoginData: null })
      this.refreshLoginState()
    } finally {
      this.setData({ isLogging: false })
      loadingManager.hide()
    }
  },

  // 步骤1.2：处理登录成功的统一方法（已简化，使用LoginService）
  step1_2_handleLoginSuccess(data) {
    SecureLogger.log('Login', '处理登录成功结果')

    // 使用LoginService处理登录成功逻辑
    const processedData = loginService.handleLoginSuccess(data)
    this.refreshLoginState()
    this.step2_analyzeLoginResult(processedData)
  },

  /**
   * 步骤2：分析登录结果并决定下一步流程
   * 功能描述：
   * - 根据登录返回的数据判断用户状态
   * - 新用户且首次登录：显示可选的用户信息授权
   * - 需要房屋认证：跳转到房屋认证引导
   * - 首次登录且需要用户信息：显示用户信息授权
   * - 其他情况：直接完成登录流程
   */
  step2_analyzeLoginResult(data) {
    const { needHouseAuth, isFirstLogin, userExists, needIdentitySelection } = data

    // 统一隐藏loading状态
    loadingManager.hide()

    // 检查是否需要身份选择
    if (needIdentitySelection) {
      this.showIdentitySelector(data)
      return
    }

    // 新用户注册成功后，提供可选的用户信息授权
    if (!userExists && isFirstLogin) {
      this.step2_1_showOptionalUserInfoAuth(data)
    } else if (needHouseAuth) {
      this.step4_showHouseAuthGuide()
    } else if (isFirstLogin && this.step5_checkNeedUserInfoAuth(data)) {
      this.step6_showUserInfoModal(data)
    } else {
      this.step9_completeLogin(data.isHouseAuth || data.isPropertyUser)
    }
  },

  /**
   * 步骤3：显示手机号绑定弹窗
   * 功能描述：
   * - 显示手机号授权弹窗
   * - 引导用户进行手机号绑定
   * - 为后续的身份验证和服务通知做准备
   */
  step3_showPhoneBinding() {
    this.setData({ showPhoneModal: true })
  },

  /**
   * 步骤4：显示房屋认证引导弹窗
   * 功能描述：
   * - 当用户手机号未绑定房屋信息时显示
   * - 提示用户联系物业确认手机号是否已录入系统
   * - 提供修改绑定手机号的选项
   * - 确保用户能够正常使用物业服务功能
   */
  step4_showHouseAuthGuide() {
    this.setData({ showHouseAuthModal: true })
  },

  /**
   * 步骤4.2：处理房屋认证弹窗关闭
   * 功能描述：
   * - 防止用户意外关闭弹窗后直接跳转首页
   * - 确保用户明确选择是否进行房屋认证
   */
  step4_2_handleHouseAuthClose() {
    // 弹窗被关闭时，重新显示弹窗，防止用户意外关闭
    this.setData({ showHouseAuthModal: true })
    wx.showToast({
      title: '请选择认证方式',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 步骤4.3：处理申请房屋认证
   * 功能描述：
   * - 用户选择申请认证时的处理逻辑
   * - 跳转到申请认证页面
   */
  step4_3_handleApplyHouseAuth() {
    // 关闭房屋认证弹窗
    this.setData({ showHouseAuthModal: false })

    // 跳转到申请认证页面
    wx.navigateTo({
      url: '/pages/auth/apply'
    })
  },

  /**
   * 步骤4.3：处理跳过房屋认证（保留原有功能）
   * 功能描述：
   * - 用户选择稍后认证时的处理逻辑
   * - 关闭弹窗但停留在登录页面，不跳转首页
   * - 用户可以重新尝试认证或关闭小程序
   */
  step4_3_handleSkipHouseAuth() {
    wx.showModal({
      title: '稍后认证',
      content: '您可以稍后联系物业登记个人信息绑定房屋，或者重新尝试修改绑定手机号。',
      showCancel: false,
      confirmText: '我知道了',
      success: () => {
        // 关闭房屋认证弹窗，停留在登录页面
        this.setData({ showHouseAuthModal: false })

        // 显示提示信息
        wx.showToast({
          title: '请完成房屋认证后使用',
          icon: 'none',
          duration: 3000
        })

      }
    })
  },

  /**
   * 步骤5：检查是否需要用户信息授权
   * 功能描述：
   * - 检查用户头像是否为默认头像或微信默认头像
   * - 检查用户昵称是否为空或默认昵称（如"微信用户"）
   * - 判断是否需要引导用户完善个人信息
   * - 为提供更好的用户体验做准备
   */
  step5_checkNeedUserInfoAuth(data) {
    const { userInfo } = data
    const needAvatar = !userInfo.avatarUrl ||
                      userInfo.avatarUrl.includes('wx.qlogo.cn') ||
                      userInfo.avatarUrl.includes('/132') ||
                      userInfo.avatarUrl.includes('thirdwx.qlogo.cn')
    const needNickname = !userInfo.nickName ||
                        userInfo.nickName === '微信用户' ||
                        userInfo.nickName.startsWith('微信用户') ||
                        userInfo.nickName.trim() === ''


    return needAvatar || needNickname
  },

  // 步骤2.1：显示新用户可选的用户信息授权
  step2_1_showOptionalUserInfoAuth(data) {
    wx.showModal({
      title: '完善个人资料',
      content: '注册成功！是否授权获取您的微信头像和昵称来完善个人资料？',
      confirmText: '授权完善',
      cancelText: '暂时跳过',
      success: (modalRes) => {
        if (modalRes.confirm) {
          // 用户选择授权，显示用户信息授权弹窗
          this.step6_showUserInfoModal(data)
        } else {
          // 用户选择跳过，直接进入下一步
          SecureLogger.log('Login', '用户跳过用户信息授权')
          if (data.needHouseAuth) {
            this.step4_showHouseAuthGuide()
          } else {
            this.step9_completeLogin(data.isHouseAuth)
          }
        }
      },
      fail: () => {
        // 默认跳过
        if (data.needHouseAuth) {
          this.step4_showHouseAuthGuide()
        } else {
          this.step9_completeLogin(data.isHouseAuth)
        }
      }
    })
  },

  /**
   * 步骤6：显示用户信息授权弹窗
   * 功能描述：
   * - 显示用户信息完善引导弹窗
   * - 提供设置头像和昵称的选项
   * - 允许用户选择跳过此步骤
   * - 为用户提供个性化的服务体验
   */
  step6_showUserInfoModal(data) {
    this.setData({
      showUserInfoModal: true,
      currentUserInfo: data.userInfo
    })
  },



  // 辅助方法：显示手机号绑定提示（已废弃，使用step3_showPhoneBinding）
  showPhoneBinding() {
    this.setData({ showPhoneModal: true })
  },

  // 辅助方法：绑定手机号通用处理（使用LoginService）
  async util_bindPhoneNumber(phoneCode, successMessage = '手机号绑定成功') {
    try {
      const userId = stateManager.getState().userInfo?.userId
      if (!userId) {
        throw new Error('用户信息缺失，请重新登录')
      }

      // 使用LoginService绑定手机号
      const bindResult = await loginService.bindPhoneNumber(phoneCode, userId)

      // 更新登录状态
      stateManager.updateLoginState({
        hasBindPhone: true,
        token: bindResult.token,
        userInfo: bindResult.userInfo,
        isHouseAuth: bindResult.isHouseAuth,
        ownerInfo: bindResult.ownerInfo,
        communityInfo: bindResult.communityInfo
      })

      this.refreshLoginState()

      if (bindResult.isHouseAuth) {
        this.util_showLoginSuccess(successMessage)
      } else {
        this.step4_showHouseAuthGuide()
      }
    } catch (error) {
      handleError(error, '手机号绑定')
    } finally {
      loadingManager.hide()
    }
  },

  // 辅助方法：关闭手机号绑定弹窗
  util_closePhoneModal() {
    this.setData({ showPhoneModal: false })
  },

  // 步骤3.1：处理手机号授权回调
  async step3_1_handlePhoneAuth(e) {
    try {
      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        SecureLogger.log('Login', '用户拒绝手机号授权')
        wx.showToast({ title: '手机号授权是必须的', icon: 'none' })

        // 如果是新用户注册流程，需要清理状态
        if (this.data.pendingLoginData) {
          this.util_handleRegistrationCancel()
        }
        return
      }

      if (!e.detail.code) {
        throw new Error('获取手机号授权码失败')
      }

      this.util_closePhoneModal()

      // 检查是否是新用户注册流程
      if (this.data.pendingLoginData) {
        SecureLogger.log('Login', '新用户注册：使用智能登录接口')

        // 验证待处理数据的完整性
        const pendingData = this.data.pendingLoginData
        if (!pendingData.checkResult) {
          throw new Error('注册数据不完整，请重新登录')
        }

        this.setData({
          isLogging: true,
          pendingLoginData: null
        })

        // 使用智能登录接口进行新用户注册，传递checkResult避免重复调用微信接口
        await this.util_performSmartLogin(null, e.detail.code, pendingData.encryptedData, pendingData.iv, pendingData.checkResult)
      } else {
        SecureLogger.log('Login', '老用户绑定手机号')
        // 老用户绑定手机号
        await this.util_bindPhoneNumber(e.detail.code)
      }
    } catch (error) {
      SecureLogger.error('Login', '手机号授权处理失败', error)
      wx.showToast({ title: error.message || '手机号授权失败', icon: 'none' })

      // 清理状态
      if (this.data.pendingLoginData) {
        this.util_handleRegistrationCancel()
      }
    }
  },

  // 步骤4.1：处理修改绑定手机号
  async step4_1_handleModifyPhone(e) {
    if (e.detail.errMsg !== "getPhoneNumber:ok") {
      wx.showToast({ title: '用户拒绝授权手机号', icon: 'none' })
      return
    }

    this.setData({ showHouseAuthModal: false })

    try {
      await this.util_updatePhoneNumber(e.detail.code)
    } catch (error) {
      // 如果更新失败，重新显示房屋认证弹窗
      this.setData({ showHouseAuthModal: true })
    }
  },

  // 辅助方法：更新手机号（不处理后续登录流程）
  async util_updatePhoneNumber(phoneCode) {
    try {
      // 直接调用更新手机号接口（无需再次调用 wx.login 获取 code）
      loadingManager.show('正在更新手机号...')
      const res = await app.request({
        url: '/api/wx/auth/bindPhone',
        method: 'POST',
        data: {
          phoneCode: phoneCode,
          userId: stateManager.getState().userInfo?.userId
        }
      })

      if (res.code === 0) {
        // 手机号更新成功后，清除登录状态，让用户重新登录
        wx.showToast({
          title: '手机号更新成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              // 清除登录状态
              stateManager.clearState(true)
              this.refreshLoginState()

              // 重置页面状态
              this.setData({
                showHouseAuthModal: false,
                showPhoneModal: false,
                isLogging: false,
                pendingLoginData: null
              })

              // 显示提示让用户重新登录
              wx.showToast({
                title: '请重新点击登录按钮',
                icon: 'none',
                duration: 3000
              })
            }, 1000)
          }
        })
      } else {
        throw new Error(res.msg || '手机号更新失败')
      }
    } catch (error) {
      handleError(error, '手机号更新')
      throw error // 重新抛出错误，让调用方处理
    } finally {
      loadingManager.hide()
    }
  },

  // 辅助方法：更新用户信息通用处理
  async util_updateUserInfo(data, successMessage) {
    try {
      await app.request({
        url: '/api/wx/auth/update',
        method: 'POST',
        data
      })

      // 更新本地存储
      const userInfo = wx.getStorageSync('wxUserInfo') || {}
      Object.assign(userInfo, data)
      wx.setStorageSync('wxUserInfo', userInfo)
      stateManager.updateLoginState({ userInfo })

      wx.showToast({ title: successMessage, icon: 'success' })
      return true
    } catch (error) {
      wx.showToast({ title: `更新失败: ${error.message}`, icon: 'none' })
      return false
    }
  },


  // 步骤6.1：关闭用户信息授权弹窗（跳过）
  step6_1_closeUserInfoModal() {
    this.setData({ showUserInfoModal: false })
    this.step9_completeLogin(stateManager.getState().isHouseAuth)
  },

  /**
   * 步骤7：开始头像授权
   * 功能描述：
   * - 关闭用户信息授权弹窗
   * - 显示头像选择弹窗
   * - 引导用户选择微信头像
   * - 为用户提供个性化头像设置
   */
  step7_startAvatarAuth() {
    this.setData({
      showUserInfoModal: false,
      showAvatarPopup: true
    })
  },

  // 步骤7.1：关闭头像弹窗
  step7_1_closeAvatarPopup() {
    this.setData({ showAvatarPopup: false })
    this.step8_checkNicknameAuth()
  },

  // 步骤7.2：处理头像选择回调
  async step7_2_handleChooseAvatar(e) {
    try {
      const { avatarUrl } = e.detail
      loadingManager.show('上传头像中...')

      const uploadResult = await this.util_uploadAvatarFile(avatarUrl)
      const success = await this.util_updateUserInfo(
        { avatarUrl: uploadResult.url },
        '头像更新成功'
      )

      this.setData({
        showAvatarPopup: false,
        'currentUserInfo.avatarUrl': uploadResult.url
      })

      setTimeout(() => this.step8_checkNicknameAuth(), success ? 1000 : 0)

    } catch (error) {
      wx.showToast({ title: '头像更新失败', icon: 'none' })
      this.step8_checkNicknameAuth()
    } finally {
      loadingManager.hide()
    }
  },



  // 辅助方法：上传头像文件到服务器
  util_uploadAvatarFile(tempFilePath) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token')
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {}

      wx.uploadFile({
        url: `${app.globalData.baseUrl}/api/wx/file/upload`,
        filePath: tempFilePath,
        name: 'file',
        formData: {
          bucketType:'public',
          source: 'avatar'
        },
        header: headers,
        success: (uploadRes) => {
          try {
            const data = JSON.parse(uploadRes.data)
            if (data.code === 0) {
              let fileUrl = data.url
              // 确保URL是完整的HTTP地址
              if (fileUrl && !fileUrl.startsWith('http')) {
                fileUrl = app.globalData.baseUrl + fileUrl
              }
              resolve({
                url: fileUrl,
                fileId: data.fileId
              })
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (parseError) {
            SecureLogger.error('Login', '上传响应解析失败', parseError)
            reject(new Error('上传响应解析失败'))
          }
        },
        fail: (error) => {
          SecureLogger.error('Login', '头像上传失败', error)
          reject(new Error(error.errMsg || '上传失败'))
        }
      })
    })
  },

  /**
   * 步骤8：检查昵称授权需求
   * 功能描述：
   * - 检查当前用户昵称是否需要完善
   * - 如果需要，尝试获取微信昵称或显示手动输入
   * - 如果不需要，直接进入登录完成流程
   * - 确保用户有合适的昵称显示
   */
  step8_checkNicknameAuth() {
    const userInfo = this.data.currentUserInfo
    const needNickname = !userInfo.nickName || userInfo.nickName === '微信用户'

    if (needNickname) {
      this.step8_1_tryGetWechatNickname()
    } else {
      this.step9_completeLogin(stateManager.getState().isHouseAuth)
    }
  },

  // 步骤8.1：尝试获取微信昵称
  async step8_1_tryGetWechatNickname() {
    try {
      loadingManager.show('获取微信昵称中...')

      const userProfile = await wx.getUserProfile({
        desc: '获取您的昵称用于完善个人资料',
        lang: 'zh_CN'
      })

      const nickname = userProfile.userInfo?.nickName
      if (nickname && nickname !== '微信用户') {
        await this.step8_2_updateNickname(nickname)
        return
      }
    } catch (error) {
      SecureLogger.log('Login', '获取微信昵称失败，显示手动输入')
    } finally {
      loadingManager.hide()
    }

    // 显示手动输入弹窗
    this.setData({
      showNicknamePopup: true,
      tempNickname: this.data.currentUserInfo.nickName || ''
    })
  },

  // 步骤8.2：更新昵称到后端
  async step8_2_updateNickname(nickname) {
    loadingManager.show('更新昵称中...')

    const success = await this.util_updateUserInfo(
      { nickName: nickname },
      '昵称更新成功'
    )

    this.setData({ 'currentUserInfo.nickName': nickname })

    setTimeout(() => {
      this.step9_completeLogin(stateManager.getState().isHouseAuth)
    }, success ? 1000 : 0)

    loadingManager.hide()
  },

  // 步骤8.3：关闭昵称弹窗
  step8_3_closeNicknamePopup() {
    this.setData({ showNicknamePopup: false, tempNickname: '' })
    this.step9_completeLogin(stateManager.getState().isHouseAuth)
  },

  // 步骤8.4：处理昵称输入
  step8_4_handleNicknameInput(e) {
    this.setData({ tempNickname: e.detail.value })
  },

  // 步骤8.5：确认昵称输入
  async step8_5_confirmNickname() {
    const nickname = this.data.tempNickname.trim()
    if (!nickname) {
      wx.showToast({ title: '请输入昵称', icon: 'none' })
      return
    }

    this.setData({ showNicknamePopup: false, tempNickname: '' })
    await this.step8_2_updateNickname(nickname)
  },



  // 辅助方法：执行智能登录请求（使用LoginService）
  async util_performSmartLogin(code, phoneCode = null, encryptedData = null, iv = null, checkResult = null) {
    try {
      // 使用LoginService执行登录
      const loginData = await loginService.performWxLogin({
        code,
        phoneCode,
        encryptedData,
        iv,
        checkResult
      })

      // 处理登录成功结果
      const processedData = loginService.handleLoginSuccess(loginData)
      this.step1_2_handleLoginSuccess(processedData)

    } catch (error) {
      SecureLogger.error('Login', '智能登录失败', error)
      handleError(error, '登录')

      // 清除登录状态和待处理数据
      loginService.clearLoginState()
      this.setData({ pendingLoginData: null })
      this.refreshLoginState()
    } finally {
      loadingManager.hide()
    }
  },

  // 注意：util_performLogin方法已删除，统一使用util_performSmartLogin

  // 步骤1.1：开始新用户注册流程（简化版）
  async step1_1_startNewUserRegistration() {
    try {
      // 确保loading被隐藏
      loadingManager.hide()

      // 检查待处理数据是否完整
      if (!this.data.pendingLoginData) {
        throw new Error('注册数据丢失，请重新登录')
      }

      SecureLogger.log('Login', '开始新用户注册流程')

      // 直接获取手机号完成基本注册
      wx.showModal({
        title: '新用户注册',
        content: '欢迎使用！需要获取您的手机号完成注册，这将用于身份验证和服务通知。',
        confirmText: '确定',
        cancelText: '取消',
        success: (modalRes) => {
          if (modalRes.confirm) {
            // 直接显示手机号授权弹窗
            this.setData({ showPhoneModal: true })
          } else {
            SecureLogger.log('Login', '用户取消注册')
            this.util_handleRegistrationCancel()
          }
        },
        fail: (error) => {
          SecureLogger.error('Login', '显示注册提示失败', error)
          this.util_handleRegistrationCancel()
        }
      })
    } catch (error) {
      SecureLogger.error('Login', '开始新用户注册失败', error)
      wx.showToast({ title: error.message || '注册失败，请重试', icon: 'none' })
      this.util_handleRegistrationCancel()
    }
  },

  // 辅助方法：处理注册取消
  util_handleRegistrationCancel() {
    this.setData({
      pendingLoginData: null,
      isLogging: false
    })
    stateManager.clearState()
    this.refreshLoginState()
  },

  // 辅助方法：获取用户信息用于注册（已废弃，保留兼容性）
  async util_getUserInfoForRegistration() {
    SecureLogger.log('Login', 'getUserInfoForRegistration方法已废弃，直接显示手机号授权')
    // 直接显示手机号授权
    this.setData({ showPhoneModal: true })
  },

  /**
   * 步骤9：完成登录流程
   * 功能描述：
   * - 只有房屋认证成功才能完成登录并跳转首页
   * - 没有房屋认证的用户不允许进入首页
   */
  step9_completeLogin(isHouseAuth) {
    if (isHouseAuth) {
      // 房屋认证成功，正常跳转首页
      this.util_showLoginSuccess('登录成功')
    } else {
      // 房屋认证未完成，不允许跳转首页，重新显示认证引导
      wx.showModal({
        title: '需要完成房屋认证',
        content: '您需要完成房屋认证才能使用小程序功能。请联系物业确认您的手机号是否已录入系统。',
        showCancel: false,
        confirmText: '重新认证',
        success: () => {
          // 重新显示房屋认证弹窗
          this.step4_showHouseAuthGuide()
        }
      })
    }
  },

  // 辅助方法：显示登录成功并跳转
  util_showLoginSuccess(message = '登录成功') {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 1000,  // 减少toast显示时间
      success: () => {
        setTimeout(() => {
          this.util_redirectToHome()
        }, 800)  // 减少跳转延迟
      }
    })
  },

  // 辅助方法：跳转到首页（使用LoginService）
  util_redirectToHome() {
    loginService.redirectToHome()
  },

  // 辅助方法：跳转到隐私政策
  util_goToPrivacy() {
    wx.navigateTo({
      url: '/pages/about/privacy'
    })
  },

  // 辅助方法：跳转到用户协议
  util_goToAgreement() {
    wx.navigateTo({
      url: '/pages/about/agreement'
    })
  },

  // 隐私政策同意状态切换
  util_onPolicyAgreementChange(event) {
    const agreed = event.detail
    this.setData({
      agreedToPolicy: agreed
    })

    // 保存协议同意状态到本地缓存
    this.savePolicyAgreementToCache(agreed)

    SecureLogger.log('Login', '隐私政策同意状态变更', { agreed })
  },

  // 暂不登录，使用演示账号登录
  async util_skipLogin() {
    try {
      SecureLogger.log('Login', '用户选择演示账号登录')

      this.setData({ isLogging: true })

      // 使用特殊code="0"标识演示登录
      const loginData = await loginService.performWxLogin({
        code: "0"  // 特殊标识，后端识别后返回演示账号信息
      })

      // 复用现有登录成功处理逻辑
      const processedData = loginService.handleLoginSuccess(loginData)
      this.refreshLoginState()
      this.step2_analyzeLoginResult(processedData)

    } catch (error) {
      SecureLogger.error('Login', '演示账号登录失败', error)
      handleError(error, '演示登录')
      this.setData({ isLogging: false })
    }
  },

  // 辅助方法：退出小程序
  util_exitMiniProgram() {
    try {
      wx.exitMiniProgram({
        success: () => {
          SecureLogger.log('Login', '小程序退出成功')
        },
        fail: (error) => {
          SecureLogger.error('Login', '小程序退出失败', error)
          // 如果无法退出，提示用户手动关闭
          wx.showModal({
            title: '提示',
            content: '无法自动退出小程序，请点击右上角关闭按钮或返回微信主界面',
            showCancel: false,
            confirmText: '知道了'
          })
        }
      })
    } catch (error) {
      SecureLogger.error('Login', '调用退出API失败', error)
      wx.showModal({
        title: '提示',
        content: '请点击右上角关闭按钮或返回微信主界面退出小程序',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  // 辅助方法：从缓存加载协议同意状态
  loadPolicyAgreementFromCache() {
    try {
      const agreed = wx.getStorageSync(STORAGE_KEYS.POLICY_AGREEMENT)
      if (agreed === true) {
        this.setData({
          agreedToPolicy: true
        })
        SecureLogger.log('Login', '从缓存恢复协议同意状态', { agreed: true })
      }
    } catch (error) {
      SecureLogger.error('Login', '读取协议同意状态缓存失败', error)
    }
  },

  // 辅助方法：保存协议同意状态到缓存
  savePolicyAgreementToCache(agreed) {
    try {
      wx.setStorageSync(STORAGE_KEYS.POLICY_AGREEMENT, agreed)
      SecureLogger.log('Login', '协议同意状态已保存到缓存', { agreed })
    } catch (error) {
      SecureLogger.error('Login', '保存协议同意状态到缓存失败', error)
    }
  },

  // 辅助方法：判断是否为网络错误
  util_isNetworkError(error) {
    if (!error) return false

    const networkErrorMessages = [
      'network error',
      'timeout',
      'request:fail',
      'network timeout',
      'connection failed',
      '网络错误',
      '连接失败',
      '请求超时'
    ]

    const errorMessage = (error.message || error.errMsg || '').toLowerCase()
    return networkErrorMessages.some(msg => errorMessage.includes(msg))
  },

  /**
   * 显示身份选择弹窗
   */
  showIdentitySelector(data) {
    const { identityResult, loginParams } = data
    const hasOwner = identityResult.hasOwnerIdentity
    const hasProperty = identityResult.hasPropertyIdentity

    const itemList = []
    if (hasOwner) itemList.push('以业主身份登录')
    if (hasProperty) itemList.push('以物业人员身份登录')

    wx.showActionSheet({
      itemList,
      success: (res) => {
        const selectedIndex = res.tapIndex
        let userType = '1' // 默认业主

        if (hasOwner && hasProperty) {
          // 两种身份都有
          userType = selectedIndex === 0 ? '1' : '2'
        } else if (hasProperty) {
          // 只有物业身份
          userType = '2'
        }

        this.performIdentityLogin(userType, loginParams, identityResult)
      },
      fail: () => {
        // 用户取消选择，重新显示选择
        setTimeout(() => {
          this.showIdentitySelector(data)
        }, 500)
      }
    })
  },

  /**
   * 执行身份选择登录
   */
  async performIdentityLogin(userType, loginParams, identityResult) {
    try {
      loadingManager.show('登录中...')

      const loginData = await loginService.selectIdentityLogin(userType, loginParams, identityResult)

      SecureLogger.log('Login', '身份选择登录成功', { userType })
      this.step2_analyzeLoginResult(loginData)

    } catch (error) {
      SecureLogger.error('Login', '身份选择登录失败', error)
      handleError(error, '登录')
    } finally {
      loadingManager.hide()
    }
  }
})