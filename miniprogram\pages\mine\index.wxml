<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="container">
  <!-- 顶部整体区域 -->
  <view class="header-wrapper">
    <!-- 背景图片 -->
    <!-- <image class="header-bg" src="/static/images/mine-bg.png" mode="aspectFill"></image> -->

    <!-- 自定义导航栏 -->
    <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
      <view class="navbar-content">
        <text class="navbar-title">个人中心</text>
      </view>
    </view>

    <!-- 业主信息卡片 -->
    <view class="user-header">
      <view class="user-card">
        <image class="avatar" src="{{userInfo.avatarUrl || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="user-info">
          <view class="nickname-container">
            <text class="nickname">姓名：{{ownerInfo.ownerName || userInfo.nickName || '未设置'}}</text>
            <view class="committee-badge" wx:if="{{ownerInfo.role == '2'}}">业委会</view>
          </view>
          <text class="phone">手机号码：{{ownerInfo.mobile || '未绑定'}}</text>
          <text class="house-info">住户：{{ownerInfo.houseStr || '暂无房屋信息'}}</text>
          <text class="area-info" wx:if="{{houseInfo.area}}">使用面积：{{houseInfo.area}}㎡</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 我的服务 -->
  <view class="section-title">我的服务</view>
  <view class="menu-list">
    <view class="menu-item" bindtap="goToHouseInfo">
      <view class="menu-item-left">
        <van-icon name="home-o" size="40rpx" color="#07c160" />
        <text>我的房屋</text>
      </view>
      <view class="menu-item-right">
        <text class="desc">{{ownerInfo.houseStr || ''}}</text>
        <van-icon name="arrow" size="24rpx" color="#ccc" />
      </view>
    </view>

    <view class="menu-item" bindtap="goToProfile">
      <view class="menu-item-left">
        <van-icon name="user-o" size="40rpx" color="#07c160" />
        <text>个人信息</text>
      </view>
      <view class="menu-item-right">
        <text class="desc">{{userInfo.mobile || '未绑定手机号'}}</text>
        <van-icon name="arrow" size="24rpx" color="#ccc" />
      </view>
    </view>

    <view class="menu-item" bindtap="goToRepairHistory">
      <view class="menu-item-left">
        <van-icon name="setting-o" size="40rpx" color="#07c160" />
        <text>我的报事报修</text>
      </view>
      <view class="menu-item-right">
        <van-icon name="arrow" size="24rpx" color="#ccc" />
      </view>
    </view>

    <view class="menu-item" bindtap="goToSuggestion">
      <view class="menu-item-left">
        <van-icon name="chat-o" size="40rpx" color="#07c160" />
        <text>我的投诉建议</text>
      </view>
      <view class="menu-item-right">
        <van-icon name="arrow" size="24rpx" color="#ccc" />
      </view>
    </view>
  </view>

  <!-- 其他功能 -->
  <view class="section-title">其他功能</view>
  <view class="menu-list">
    <view class="menu-item" bindtap="goToMore">
      <view class="menu-item-left">
        <van-icon name="apps-o" size="40rpx" color="#07c160" />
        <text>更多服务</text>
      </view>
      <view class="menu-item-right">
        <van-icon name="arrow" size="24rpx" color="#ccc" />
      </view>
    </view>

    <view class="menu-item" bindtap="goToAbout">
      <view class="menu-item-left">
        <van-icon name="info-o" size="40rpx" color="#07c160" />
        <text>关于我们</text>
      </view>
      <view class="menu-item-right">
        <van-icon name="arrow" size="24rpx" color="#ccc" />
      </view>
    </view>

    <button class="contact-btn" open-type="contact">
      <view class="menu-item">
        <view class="menu-item-left">
          <van-icon name="service" size="40rpx" color="#07c160" />
          <text>联系客服</text>
        </view>
        <view class="menu-item-right">
          <van-icon name="arrow" size="24rpx" color="#ccc" />
        </view>
      </view>
    </button>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-btn" bindtap="handleLogout">退出登录</view>

  <!-- 版本信息 -->
  <view class="version-info">当前版本 20250723.1</view>
</view> 