import {getStateManager} from '../../utils/stateManager.js'
import {getLoadingManager, handleError} from '../../utils/errorHandler.js'
import {getUserRoleName} from '../../utils/common.js'
import {getSystemInfoSyncCompat} from '../../utils/systemInfoCompat.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()

Page({
  data: {
    userInfo: null,
    ownerInfo: null,
    communityInfo: null,
    houseInfo: null,
    statusBarHeight: 20
  },

  onLoad() {
    this.initializePage();
  },

  onShow() {
    // 设置tabBar（使用公共工具函数）
    const { initTabBar } = require('../../utils/pageUtils.js')
    initTabBar('1')

    // 检查是否需要刷新数据
    if (stateManager.checkAndClearRefresh()) {
      console.log('[Mine] 检测到全局刷新标记，刷新用户数据')
    }

    this.refreshUserData()
  },

  // 初始化页面
  initializePage() {
    this.getSystemInfo()
    this.refreshUserData()
  },

  // 刷新用户数据
  refreshUserData() {
    const state = stateManager.getState()
    this.setData({
      userInfo: state.userInfo,
      ownerInfo: state.ownerInfo,
      communityInfo: state.communityInfo,
      houseInfo: state.houseInfo
    })
  },

  // 导航方法
  goToHouseInfo() {
    wx.navigateTo({
      url: '/pages/house/index'
    })
  },

  goToProfile() {
    this.recordMenuClick('profile', '个人信息', 'page', 'mine')
    wx.navigateTo({
      url: '/pages/profile/index'
    })
  },

  goToRepairHistory() {
    this.recordMenuClick('repair_history', '报修记录', 'page', 'mine')
    wx.navigateTo({
      url: '/pages/bx/history'
    })
  },

  goToSuggestion() {
    this.recordMenuClick('suggestion_history', '投诉建议', 'page', 'mine')
    wx.navigateTo({
      url: '/pages/complaint/history'
    })
  },

  goToMore() {
    this.recordMenuClick('more_service', '更多服务', 'page', 'mine')
    wx.navigateTo({
      url: '/pages/mine/more'
    })
  },

  goToAbout() {
    this.recordMenuClick('about', '关于我们', 'page', 'mine')
    wx.navigateTo({
      url: '/pages/about/index'
    })
  },

  // 退出登录（唯一的清除token方式）
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmText: '退出',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performLogout()
        }
      }
    })
  },

  // 执行退出登录（唯一的真正清除token方式）
  async performLogout() {
    try {
      loadingManager.show('退出中...')

      // 停止token管理器的定时检查
      try {
        const tokenManager = require('../../utils/tokenManager.js').default
        if (tokenManager && typeof tokenManager.stopTokenCheck === 'function') {
          tokenManager.stopTokenCheck()
          console.log('[Logout] 已停止token定时检查')
        }
      } catch (error) {
        console.warn('[Logout] 停止token检查失败:', error)
      }

      // 调用退出登录接口
      try {
        await app.request({
          url: '/api/wx/auth/logout',
          method: 'POST'
        })
        console.log('[Logout] 退出登录接口调用成功')
      } catch (error) {
        // 即使接口调用失败，也要清除本地状态
        console.warn('[Logout] 退出登录接口调用失败，但继续清除本地状态:', error)
      }

      // 强制清除状态管理器和本地存储（绕过智能保护）
      stateManager.clearState(true) // 传入true表示强制清除

      // 额外清除token管理器状态
      try {
        const tokenManager = require('../../utils/tokenManager.js').default
        if (tokenManager && typeof tokenManager.clearToken === 'function') {
          tokenManager.clearToken()
          tokenManager.resetRetryCount()
          console.log('[Logout] 已清除token管理器状态')
        }
      } catch (error) {
        console.warn('[Logout] 清除token管理器状态失败:', error)
      }

      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })

      // 跳转到登录页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/index'
        })
      }, 1500)

    } catch (error) {
      handleError(error, '退出登录')
    } finally {
      loadingManager.hide()
    }
  },

  // 获取用户角色名称
  getUserRoleText() {
    const role = this.data.ownerInfo?.role
    return getUserRoleName(role)
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = getSystemInfoSyncCompat()
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight || 20
      })
    } catch (error) {
      console.warn('[SystemInfo] 获取系统信息失败:', error)
      this.setData({ statusBarHeight: 20 })
    }
  },

  /**
   * 记录菜单点击日志
   * @param {string} menuId 菜单ID
   * @param {string} menuName 菜单名称
   * @param {string} menuType 菜单类型
   * @param {string} source 来源页面
   */
  recordMenuClick(menuId, menuName, menuType, source) {
    // 静默记录，不影响用户体验
    try {
      console.log('[Mine] 记录菜单点击:', { menuId, menuName, menuType, source })

      app.request({
        url: '/api/wx/index/recordMenuClick',
        method: 'POST',
        data: {
          menuId: menuId || '',
          menuName: menuName || '',
          menuType: menuType || '',
          source: source || ''
        }
      }).catch(error => {
        // 静默处理错误，不影响用户体验
        console.warn('[Mine] 记录菜单点击日志失败:', error)
      })
    } catch (error) {
      console.warn('[Mine] 记录菜单点击日志异常:', error)
    }
  }

})