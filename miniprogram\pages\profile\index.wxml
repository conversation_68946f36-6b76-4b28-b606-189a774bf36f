<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="container">
  <view class="profile-list">
    <view class="profile-item avatar-item" bindtap="chooseAvatar">
      <image class="avatar" src="{{userInfo.avatarUrl || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="avatar-action">
        <text class="action-text">更新头像</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>
    </view>

    <view class="profile-item nickname-item" bindtap="editNickname">
      <text class="label">昵称</text>
      <view class="value">
        <text>{{userInfo.nickName || '点击设置昵称'}}</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>
    </view>

    <view class="profile-item">
      <text class="label">姓名</text>
      <view class="value">
        <view class="name-container">
          <text>{{ownerInfo.ownerName || ''}}</text>
          <view class="committee-badge" wx:if="{{ownerInfo.role == '2'}}">业委会</view>
        </view>
      </view>
    </view>

    <view class="profile-item">
      <text class="label">手机号</text>
      <view class="value">
        <block wx:if="{{ownerInfo.mobile}}">
          <text>{{ownerInfo.mobile}}</text>
          <view class="phone-action" bindtap="updatePhone">
            <text class="btn-text">更换</text>
            <van-icon name="arrow" size="16px" color="#07c160" />
          </view>
        </block>
        <block wx:else>
          <view class="phone-action" bindtap="bindPhone">
            <text class="btn-text">去绑定</text>
            <van-icon name="arrow" size="16px" color="#07c160" />
          </view>
        </block>
      </view>
    </view>

    <view class="profile-item">
      <text class="label">我的小区</text>
      <text class="value">{{communityInfo.communityName}}</text>
    </view>

    <view class="profile-item">
      <text class="label">我的房屋</text>
      <view class="value">
        <text class="auth-text">{{ownerInfo.houseStr || ''}}</text>
        <view class="auth-action" bindtap="goToAuth" wx:if="{{!isAuth}}">
          <text class="btn-text">去认证</text>
          <van-icon name="arrow" size="16px" color="#07c160" />
        </view>
      </view>
    </view>
  </view>

  <!-- 头像选择弹窗 -->
  <view class="popup-mask" wx:if="{{showAvatarPopup}}" bindtap="closeAvatarPopup">
    <view class="popup-content" catchtap="stopPropagation">
      <view class="popup-title">选择头像</view>
      <view class="popup-desc">点击下方按钮选择微信头像</view>
      <button class="auth-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
        使用微信头像
      </button>
      <view class="popup-close" bindtap="closeAvatarPopup">取消</view>
    </view>
  </view>



  <!-- 昵称编辑弹窗 -->
  <view class="popup-mask" wx:if="{{showNicknamePopup}}" bindtap="closeNicknamePopup">
    <view class="popup-content" catchtap="stopPropagation">
      <view class="popup-title">设置昵称</view>
      <view class="popup-desc">请输入您的昵称</view>
      <input
        class="nickname-edit-input"
        type="nickname"
        placeholder="请输入昵称"
        value="{{tempNickname}}"
        bindinput="onNicknameInput"
        maxlength="20"
        focus="{{true}}"
      />
      <view class="popup-buttons">
        <view class="popup-btn cancel-btn" bindtap="closeNicknamePopup">取消</view>
        <view class="popup-btn confirm-btn" bindtap="confirmNickname">确定</view>
      </view>
    </view>
  </view>

  <!-- 手机号授权弹窗 -->
  <view class="popup-mask" wx:if="{{showPhonePopup}}" bindtap="closePhonePopup">
    <view class="popup-content" catchtap="stopPropagation">
      <view class="popup-title">{{ownerInfo.mobile ? '更换手机号' : '绑定手机号'}}</view>
      <view class="popup-desc">{{ownerInfo.mobile ? '点击下方按钮更换手机号' : '为了给您提供更好的服务，请绑定手机号'}}</view>
      <button class="auth-btn" open-type="getPhoneNumber" bindgetphonenumber="onGetPhoneNumber">
        {{ownerInfo.mobile ? '使用微信手机号' : '一键绑定手机号'}}
      </button>
      <view class="popup-close" bindtap="closePhonePopup">取消</view>
    </view>
  </view>
</view> 